<template>
  <div class="order-confirm">
    <section class="order-confirm__address">
      <AddressSkeleton v-if="addressLoading" />
      <div v-else class="address-card" @click="handleSelectAddress">
        <div v-if="isAddressComplete" class="address-card__content">
          <div class="address-card__region">{{ selectedAddress.region }}</div>
          <div class="address-card__detail">{{ selectedAddress.detailAddress }}</div>
          <div class="address-card__contact">
            <span class="contact__name">{{ selectedAddress.name }}</span>
            <span class="contact__phone">{{ selectedAddress.phone }}</span>
          </div>
        </div>
        <div v-else class="address-card__placeholder">
          <div class="placeholder__icon">
            <img src="@/static/images/location.png" alt="地址" class="location-icon" loading="lazy" />
          </div>
          <div class="placeholder__content">
            <div class="placeholder__title">请选择收货地址</div>
            <div class="placeholder__subtitle">选择收货地址后才能下单</div>
          </div>
        </div>
        <img src="@/static/images/arrow-right-gray.png" alt="选择地址" class="address-card__arrow" loading="lazy" />
      </div>
    </section>

    <section class="order-confirm__goods">
      <GoodsListSkeleton v-if="goodsLoading" />
      <OrderConfirmGoodsListLayout
        v-else-if="!goodsLoading && goodsList.length > 0"
        :goods-list="goodsList"
        :image-size="65"
        :min-height="65"
        :show-actions="false"
      />
    </section>

    <section class="order-confirm__summary">
      <WoCard v-if="summaryLoading">
        <SummarySkeleton />
      </WoCard>
      <WoCard v-else>
        <InfoRow label="商品总价">
          <template #value>
            <PriceDisplay :price="orderSummary.goodsTotal" size="small" />
          </template>
        </InfoRow>
        <InfoRow label="运费" :value="orderSummary.shippingText" />
        <InfoRow
          label="慰问活动"
          :value="orderSummary.activityText"
          :value-class="orderSummary.activityText ? 'activity-text' : 'activity-text activity-text--empty'"
          :show-arrow="true"
          @click="handleShowQuotaPopup"
        />
        <InfoRow label="实付款">
          <template #value>
            <PriceDisplay :price="orderSummary.actualPayment" size="small" color="orange" />
          </template>
        </InfoRow>
      </WoCard>
    </section>

    <WoActionBarPlaceholder />

    <WoActionBar>
      <div class="order-confirm__submit">
        <div class="submit__info">
          <span class="submit__price">
            <PriceDisplay :price="summaryLoading ? '' : orderSummary.actualPayment" size="large" color="orange" />
          </span>
          <div class="submit__details">
            <span v-if="summaryLoading">额度抵扣 --.-</span>
            <span v-else-if="orderSummary.savingsText">{{ orderSummary.savingsText }}</span>
            <span v-if="summaryLoading">自付金额 --.-</span>
            <span v-else-if="orderSummary.refundText">{{ orderSummary.refundText }}</span>
          </div>
        </div>
        <WoButton type="gradient" size="special" @click="handleSubmitOrder">
          提交订单
        </WoButton>
      </div>
    </WoActionBar>

    <AddressQuickSelectionPopup
      v-if="showAddressPopup"
      v-model:visible="showAddressPopup"
      @select="handleAddressSelected"
      @create="handleCreateAddress"
      @edit="handleEditAddress"
    />

    <QuotaInfoPopup
      v-if="showQuotaPopup"
      v-model="showQuotaPopup"
      :available-activity-quota="availableActivityQuota"
      :un-available-activity-quota="unAvailableActivityQuota"
      @change="handleQuotaChange"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent, nextTick, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { useUserStore } from '@/store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import {
  get,
  isEmpty,
  debounce,
  throttle,
  compact,
  some,
  flatMap,
  reduce,
  filter,
  forEach,
  map
} from 'lodash-es'
import Decimal from 'decimal.js'

import PriceDisplay from '@/components/Common/PriceDisplay.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderConfirmGoodsListLayout from '@views/WoMall/Order/components/OrderConfirmGoodsListLayout.vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import InfoRow from '@components/Common/InfoRow.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import AddressSkeleton from '@views/WoMall/Order/components/AddressSkeleton.vue'
import GoodsListSkeleton from '@views/WoMall/Order/components/GoodsListSkeleton.vue'
import SummarySkeleton from '@views/WoMall/Order/components/SummarySkeleton.vue'

const AddressQuickSelectionPopup = defineAsyncComponent(() =>
  import('@/components/Address/AddressQuickSelectionPopup.vue')
)
const QuotaInfoPopup = defineAsyncComponent(() =>
  import('@/components/Common/QuotaInfoPopup/QuotaInfoPopup.vue')
)

import { getBuyNowGoods, getSelectedGoods, getFuLiHuiID, jdAddressCheck } from '@/api'
import { submitOrder, queryAmount } from '@/api/interface/order.js'
import { getLimitAreaList } from '@/api/interface/goods.js'
import { getBizCode } from '@/utils/curEnv.js'
import { buyProductNow, buyProductNowSession, buyProductCart, buyProductCartSession, curDeveloperId, curChannelBiz } from '@/utils/storage'
import { formSubmit } from 'commonkit'
import { fenToYuan } from '@/utils/amount'
import { useAlert } from '@/hooks/index.js'
const $alert = useAlert()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const cartStore = useNewCartStore()

const JD_GOODS_CODE = 'JD'
const ORDER_TYPE = {
  BUY_NOW: '1',
  CART: '2'
}

const state = ref({
  showAddressPopup: false,
  showQuotaPopup: false,
  loading: false,
  addressLoading: true,
  goodsLoading: true,
  summaryLoading: true,
  orderType: '',
  orderCartList: [],
  cartPriceTotal: 0,
  totalFreight: 0,
  isJD: false,
  isSubmit: false,
  isRemoveGifts: false,
  orderVoInfo: null,
  dialogShow: false,
  wapay: {
    encryptContent: '',
    wapURL: '',
    bizOrderId: ''
  },
  developerId: '',
  isRegionalPurchase: false,
  availableActivityQuota: {},
  unAvailableActivityQuota: {},
  activeName: '',
  quotaPayment: 0,
  selfPayment: 0
})

const {
  showAddressPopup,
  showQuotaPopup,
  loading,
  addressLoading,
  goodsLoading,
  summaryLoading,
  orderType,
  orderCartList,
  cartPriceTotal,
  totalFreight,
  isJD,
  isSubmit,
  isRemoveGifts,
  orderVoInfo,
  dialogShow,
  wapay,
  developerId,
  isRegionalPurchase,
  availableActivityQuota,
  unAvailableActivityQuota,
  activeName,
  quotaPayment,
  selfPayment
} = toRefs(state.value)

const selectedAddress = computed(() => {
  const addressInfo = userStore.curAddressInfo
  return {
    name: get(addressInfo, 'recName', ''),
    phone: get(addressInfo, 'recPhone', ''),
    region: compact([
      get(addressInfo, 'provinceName'),
      get(addressInfo, 'cityName'),
      get(addressInfo, 'countyName')
    ]).join(''),
    detailAddress: get(addressInfo, 'addrDetail', '')
  }
})

const isAddressComplete = computed(() => {
  const addressInfo = userStore.curAddressInfo
  const requiredFields = ['recName', 'recPhone', 'provinceName', 'cityName', 'countyName', 'addrDetail']
  return requiredFields.every(field => !isEmpty(get(addressInfo, field)))
})

const goodsList = computed(() => {
  return get(orderCartList.value, '[0].goodsList', [])
})

const orderSummary = computed(() => {
  const priceInCents = cartPriceTotal.value * 100
  return {
    goodsTotal: priceInCents,
    shippingText: isJD.value && totalFreight.value ? '运费已分摊至商品金额' : '免运费',
    activityText: activeName.value || '暂无活动',
    actualPayment: priceInCents,
    savingsText: quotaPayment.value >= 0 ? `额度抵扣 ¥${quotaPayment.value}` : '',
    refundText: selfPayment.value >= 0 ? `自付金额 ¥${selfPayment.value}` : ''
  }
})

// 处理立即购买数据
const processBuyNowData = (data) => {
  orderCartList.value = [{ goodsList: [] }]

  // 合并有效和无效商品列表
  const allGoodsList = [].concat(data.validGoodsList || [], data.invalidGoodsList || [])

  // 转换数据格式
  // 优化数据处理以匹配 OrderConfirmGoodsListLayout 组件需求
  orderCartList.value[0].goodsList = allGoodsList.map(item => {
    const goods = item.goods
    const sku = get(goods, 'skuList[0]', {})

    // 拼接规格信息
    const specParams = ['param', 'param1', 'param2', 'param3', 'param4', 'param5']
    const specParts = compact(specParams.map(param => get(sku, param)))

    return {
      id: `${get(sku, 'goodsId', '')}_${get(sku, 'skuId', '')}`,
      name: get(sku, 'name', ''),
      price: item.nowPrice || 0,
      quantity: item.skuNum || 0,
      detailImageUrl: get(sku, 'listImageUrl', ''),
      skuNeedGift: item.skuNeedGift,
      rawData: item,
      // 添加 OrderGoodsCard 组件需要的数据结构
      skuNumInfoList: [{
        sku: {
          ...sku,
          goodsId: get(sku, 'goodsId', ''),
          skuId: get(sku, 'skuId', ''),
          name: get(sku, 'name', ''),
          detailImageUrl: get(sku, 'listImageUrl', ''),
          param: get(sku, 'param', ''),
          param1: get(sku, 'param1', ''),
          param2: get(sku, 'param2', ''),
          param3: get(sku, 'param3', ''),
          param4: get(sku, 'param4', ''),
          price: item.nowPrice || 0
        },
        skuNum: item.skuNum || 0
      }]
    }
  })

  cartPriceTotal.value = parseFloat((data.cartPriceTotal / 100).toFixed(2))
  totalFreight.value = data.totalFreight

  // 检查是否有京东商品
  checkJDGoods()
}

// 检查是否包含京东商品
const checkJDGoods = () => {
  isJD.value = orderCartList.value.some(goodGroup =>
    goodGroup.goodsList.some(goodsItem =>
      goodsItem.goods?.skuList?.some(sku =>
        sku.supplierCode?.includes(JD_GOODS_CODE)
      )
    )
  )
}

// 区域购买限制检查
const checkRegionalRestrictions = async () => {
  const curAddrInfo = userStore.curAddressInfo

  // 构建商品ID列表
  const goodsIdList = orderCartList.value.flatMap(supplier =>
    supplier.goodsList.map(skuInfo => {
      // 初始化限制字段
      skuInfo.nosale = false
      // 收集商品ID
      return get(skuInfo, 'rawData.cartGoodsId')
    })
  ).filter(Boolean)

  // 构建地址信息
  const addressFields = ['provinceId', 'cityId', 'countyId', 'townId']
  const areaInfo = addressFields.reduce((acc, field) => {
    const value = get(curAddrInfo, field)
    if (value) acc[field] = value
    return acc
  }, {})

  // 如果地址信息为空，直接返回
  if (isEmpty(areaInfo)) {
    return
  }

  const params = {
    area: JSON.stringify(areaInfo),
    goodsIdList: goodsIdList
  }

  try {
    showLoadingToast()
    const [err, json] = await getLimitAreaList(params)
    closeToast()

    if (!err) {
      if (json && json.length > 0) {
        // 存在限购，增加商品锁定
        orderCartList.value.forEach((supplier) => {
          supplier.goodsList.forEach((skuInfo) => {
            json.forEach((item) => {
              // 匹配skuId
              if (item === skuInfo.rawData?.cartGoodsId) {
                skuInfo.nosale = true
              }
            })
          })
        })
        isRegionalPurchase.value = true
      } else {
        // 不存在限购，解除商品锁定
        orderCartList.value.forEach((supplier) => {
          supplier.goodsList.forEach((skuInfo) => {
            skuInfo.nosale = false
          })
        })
        isRegionalPurchase.value = false
      }
    } else {
      showToast(err.msg)
    }

    // 如果是京东商品且有运费，显示提示
    if (isJD.value && totalFreight.value) {
      showToast('运费已分摊至商品金额')
    }

    console.warn('isRegionalPurchase:', isRegionalPurchase.value)
  } catch (error) {
    closeToast()
    console.error('检查区域限制失败:', error)
  }
}

// 处理立即购买订单
const handleBuyNowOrder = async () => {
  const { goodsId, skuId, goodsNum } = route.query
  const addressInfo = JSON.stringify(userStore.curAddressInfo)

  orderType.value = ORDER_TYPE.BUY_NOW

  // 先尝试从缓存获取数据
  const cachedData = buyProductNow.get() || buyProductNowSession.get()

  if (cachedData) {
    processBuyNowData(cachedData.data)
    if (cachedData.code !== '0000') {
      showToast(cachedData.msg)
      isSubmit.value = false
      return false
    }
    isSubmit.value = true
    return true
  }

  // 缓存中没有数据，调用API
  try {
    showLoadingToast()

    const [res, data] = await getBuyNowGoods({
      goodsId,
      skuId,
      goodsNum,
      addressInfo,
      bizCode: getBizCode('ORDER')
    })

    closeToast()

    if (res.code !== '0000') {
      showToast(res.msg)
      isSubmit.value = false
      return false
    }

    processBuyNowData(data)
    isSubmit.value = true
    return true

  } catch (error) {
    closeToast()
    showToast('加载订单信息失败')
    isSubmit.value = false
    return false
  }
}

// 处理购物车订单
const handleCartOrder = async () => {
  const addressInfo = JSON.stringify(userStore.curAddressInfo)

  orderType.value = ORDER_TYPE.CART

  const buyGoodsList = buyProductCart.get() || buyProductCartSession.get()

  if (!buyGoodsList) {
    console.error('购物车下单缓存数据为空')
    showToast('购物车数据异常')
    isSubmit.value = false
    return false
  }

  try {
    showLoadingToast()

    const [res, data] = await getSelectedGoods({
      bizCode: getBizCode('ORDER'),
      addressInfo,
      buyGoodsList: JSON.stringify(buyGoodsList)
    })

    closeToast()

    if (res.code !== '0000') {
      showToast(res.msg)
      isSubmit.value = false
      return false
    }

    processBuyNowData(data)
    isSubmit.value = true
    return true

  } catch (error) {
    closeToast()
    showToast('加载订单信息失败')
    isSubmit.value = false
    return false
  }
}

// 加载地址信息
const loadAddressData = async () => {
  addressLoading.value = true
  try {
    await userStore.queryDefaultAddr()
  } catch (error) {
    console.error('加载地址失败:', error)
    showToast('加载地址失败')
  } finally {
    addressLoading.value = false
  }
}

// 加载商品数据
const loadGoodsData = async () => {
  goodsLoading.value = true
  try {
    const { goodsId, skuId, goodsNum } = route.query
    const isDirectBuy = goodsId && skuId && goodsNum

    let success = false

    if (isDirectBuy) {
      success = await handleBuyNowOrder()
    } else {
      success = await handleCartOrder()
    }

    if (success) {
      // 显示运费提示
      if (isJD.value && totalFreight.value) {
        showToast('运费已分摊至商品金额')
      }

      // 检查区域限制
      await checkRegionalRestrictions()
    }

    return success
  } catch (error) {
    console.error('加载商品数据失败:', error)
    showToast('加载商品数据失败')
    return false
  } finally {
    goodsLoading.value = false
  }
}

// 加载订单汇总数据
const loadSummaryData = async () => {
  summaryLoading.value = true
  try {
    // 获取支付详情和活动信息
    await getPaymentDetails()
  } catch (error) {
    console.error('加载订单汇总失败:', error)
    showToast('加载订单汇总失败')
  } finally {
    summaryLoading.value = false
  }
}

// 初始化订单数据 - 从上到下依次加载策略
const initOrderData = async () => {
  if (loading.value) return

  loading.value = true

  try {
    // 第一步：加载地址信息（最上方区域）
    await loadAddressData()

    // 等待一小段时间，让用户看到地址加载完成
    await nextTick()

    // 第二步：加载商品数据（中间区域）
    const goodsSuccess = await loadGoodsData()

    // 等待一小段时间，让用户看到商品加载完成
    await nextTick()

    // 第三步：加载订单汇总数据（底部区域）
    await loadSummaryData()

    // 如果商品加载失败，显示错误但不阻止其他功能
    if (!goodsSuccess) {
      console.warn('商品数据加载失败，但页面仍可使用')
    }

  } catch (error) {
    console.error('初始化订单失败:', error)
    showToast('初始化订单失败')
  } finally {
    loading.value = false
  }
}

// 选择地址 - 显示地址选择弹窗 - 使用防抖避免重复点击
const handleSelectAddress = debounce(() => {
  showAddressPopup.value = true
}, 300)

// 地址检查函数
const addressCheck = async () => {
  showLoadingToast()
  const [err, json] = await jdAddressCheck()
  closeToast()

  if (err) {
    showToast(err.msg)
    return false
  }

  if (!json) {
    $alert({
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存',
      confirmButtonText: '修改地址',
      showCancelButton: true,
      cancelButtonText: '取消',
      messageAlign: 'center',
      onConfirmCallback: () => {
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      },
      onCancelCallback: () => { }
    })

    return false
  }

  return true
}

// 地址选择完成
const handleAddressSelected = async (address) => {
  showToast('地址选择成功')

  // 更新购物车数据 (如果userStore有此方法)
  cartStore.query()

  // 地址检查
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  const { goodsId, skuId, goodsNum } = route.query
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  if (goodsId && skuId && goodsNum) {
    orderType.value = ORDER_TYPE.BUY_NOW // 代表直接下单
    showLoadingToast()

    try {
      const [res, json] = await getBuyNowGoods({
        goodsId,
        skuId,
        goodsNum,
        addressInfo,
        bizCode: getBizCode('ORDER')
      })

      closeToast()

      if (res.code !== '0000') {
        isSubmit.value = false
        showToast(res.msg)
      } else {
        isSubmit.value = true
        processBuyNowData(json)
        if (isJD.value && totalFreight.value) {
          showToast('运费已分摊至商品金额')
        }
      }
    } catch (error) {
      closeToast()
      console.error('重新计算订单失败:', error)
      showToast('重新计算订单失败')
    }
  } else {
    showLoadingToast()
    orderType.value = ORDER_TYPE.CART // 代表购物车下单

    const buyGoodsList = buyProductCart.get() || buyProductCartSession.get()
    if (!buyGoodsList) {
      console.error('buyProductCart购车下单（提交订单页面）缓存数据为空', {
        bizCode: getBizCode('ORDER'),
        addressInfo: addressInfo,
        buyGoodsList: JSON.stringify(buyGoodsList)
      })
    }

    try {
      const [res, json] = await getSelectedGoods({
        bizCode: getBizCode('ORDER'),
        addressInfo: addressInfo,
        buyGoodsList: JSON.stringify(buyGoodsList)
      })

      closeToast()

      if (res.code !== '0000') {
        isSubmit.value = false
        showToast(res.msg)
      } else {
        isSubmit.value = true
        processBuyNowData(json)
        if (isJD.value && totalFreight.value) {
          showToast('运费已分摊至商品金额')
        }
      }
    } catch (error) {
      closeToast()
      console.error('重新计算订单失败:', error)
      showToast('重新计算订单失败')
    }
  }

  // 检查区域购买限制
  await checkRegionalRestrictions()
}

// 新建地址
const handleCreateAddress = () => {
  router.push('/addr/add')
}

// 编辑地址
const handleEditAddress = (address) => {
  router.push(`/addr/edit/${address.addressId}`)
}

// 显示慰问活动弹窗
const handleShowQuotaPopup = async (event) => {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  // 检查是否已有可用活动数据
  const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])

  if (availableActivityQuotaList && availableActivityQuotaList.length > 0) {
    showQuotaPopup.value = true
    return
  }

  // 构建请求参数
  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(cartPriceTotal.value).mul(new Decimal(100)).toString())
  }

  showQuotaPopup.value = true
  showLoadingToast()

  try {
    const [err, json] = await queryAmount(params)
    closeToast()

    if (!err) {
      availableActivityQuota.value = json
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    console.error('获取慰问活动数据失败:', error)
    showToast('获取活动信息失败')
  }
}

// 获取支付详情和活动信息
const getPaymentDetails = async (orderPrice = null) => {
  // 如果没有传入价格且当前价格为0，则跳过
  const priceToUse = orderPrice || cartPriceTotal.value
  if (priceToUse === 0) {
    return
  }

  showLoadingToast()
  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(priceToUse).mul(new Decimal(100)).toString())
  }
  const [err, json] = await queryAmount(params)
  closeToast()
  if (!err) {
    availableActivityQuota.value = json
    const grantAmount = get(json, 'grantAmount', 0)
    const balanceAmount = get(json, 'amount', 0)
    const realPrice = priceToUse * 100
    if (grantAmount > 0) {
      const activityName = get(json, 'activityName', '')
      activeName.value = activityName

      if (!activityName) {
        quotaPayment.value = fenToYuan(0)
        selfPayment.value = fenToYuan(realPrice)
        return
      }

      if (realPrice > balanceAmount) {
        quotaPayment.value = fenToYuan(balanceAmount)
        selfPayment.value = fenToYuan(realPrice - balanceAmount)
      } else {
        quotaPayment.value = fenToYuan(realPrice)
        selfPayment.value = fenToYuan(0)
      }
    } else {
      const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])
      const activityName = get(availableActivityQuotaList[0], 'activityName', '')
      activeName.value = activityName
      if (!activityName) {
        quotaPayment.value = fenToYuan(0)
        selfPayment.value = fenToYuan(realPrice)
        return
      }

      if (availableActivityQuotaList.length > 0) {
        const { balanceAmount } = availableActivityQuotaList[0]
        if (realPrice > balanceAmount) {
          quotaPayment.value = fenToYuan(balanceAmount)
          selfPayment.value = fenToYuan(realPrice - balanceAmount)
          console.warn(123123231, quotaPayment.value, selfPayment.value)
        } else {
          quotaPayment.value = fenToYuan(realPrice)
          selfPayment.value = fenToYuan(0)
        }
      }
    }
  } else {
    showToast(err.msg)
  }
}

// 处理慰问活动选项卡切换
const handleQuotaChange = (tabIndex) => {
  console.log('切换到选项卡:', tabIndex)
}

// 提交订单
const handleSubmitOrder = throttle(async () => {
  if (!isSubmit.value) {
    return
  }

  const curAddrInfo = userStore.curAddressInfo

  // 检查地址信息
  if (curAddrInfo.type === 2 || curAddrInfo.type === 3) {
    $alert({
      message: curAddrInfo.type === 2
        ? '您还未选择收货地址，是否新建收货地址？'
        : `您在浏览中选择${curAddrInfo.provinceName}${curAddrInfo.cityName}，是否新建收货地址？`,
      confirmButtonText: '新建地址',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: () => {
        router.push({ name: 'address-add', query: curAddrInfo })
      },
      onCancelCallback: () => {
        userStore.setTempAddr(null)
      }
    })
    return
  }

  // 检查所选地址是否支持销售 - 使用 lodash some 优化
  const hasUnsupportedGoods = orderCartList.value.some(supplier =>
    supplier.goodsList.some(skuInfo => skuInfo.nosale)
  )

  if (hasUnsupportedGoods) {
    showToast('当前部分商品在所选地区暂不支持销售，请更改收货地址或者返回重新选择商品')
    return
  }

  // 获取开发者ID（福利汇业务）
  let currentDeveloperId = ''
  if (getBizCode() === 'fulihui') {
    currentDeveloperId = curDeveloperId.get()
    if (!currentDeveloperId) {
      showLoadingToast()
      try {
        const [err, json] = await getFuLiHuiID({ bizCode: getBizCode('QUERY') })
        closeToast()
        if (!err) {
          currentDeveloperId = json || ''
          curDeveloperId.set(currentDeveloperId)
        }
      } catch (error) {
        closeToast()
        console.error('获取福利汇ID失败:', error)
      }
    }
  }

  // 构建订单信息
  if (!isRemoveGifts.value) {
    orderVoInfo.value = {
      disriBiz: getBizCode('ORDER'),
      bizChannelCode: curChannelBiz.get(),
      addressInfo: { ...curAddrInfo },
      developerInfo: {
        developerId: developerId.value || currentDeveloperId,
        proxyChannel: ''
      },
      cartGoodsList: [],
      isDirectPay: null
    }

    const cartGoodsList = []
    orderCartList.value.forEach((supplier) => {
      supplier.goodsList.forEach((skuInfo) => {
        cartGoodsList.push({
          cartGoodsId: skuInfo.rawData?.cartGoodsId,
          cartSkuId: skuInfo.rawData?.cartSkuId,
          skuNum: skuInfo.quantity,
          supplierCode: skuInfo.rawData?.supplierCode,
          skuNeedGift: true
        })
      })
    })
    orderVoInfo.value.cartGoodsList = cartGoodsList
  }

  // 设置订单类型
  if (orderType.value === ORDER_TYPE.BUY_NOW) {
    orderVoInfo.value.isDirectPay = true
  } else if (orderType.value === ORDER_TYPE.CART) {
    orderVoInfo.value.isDirectPay = false
  } else {
    return
  }

  orderVoInfo.value.bizChannelCode = curChannelBiz.get()

  // 添加保证金相关参数
  if (route.query.curSelectedMoney) {
    orderVoInfo.value.bondPrice = +route.query.curSelectedMoney
  }
  if (route.query.curSelectedTime) {
    orderVoInfo.value.bondTerm = (+route.query.curSelectedTime) * 12
  }
  if (route.query.orderNo) {
    orderVoInfo.value.bondOrderId = route.query.orderNo
  }

  // 提交订单
  try {
    showLoadingToast()
    const [err, json] = await submitOrder({
      orderVoInfo: JSON.stringify(orderVoInfo.value)
    })
    closeToast()

    if (!err) {
      if (getBizCode() === 'fupin' && json.isNeedCompanyInsert === 'true') {
        dialogShow.value = true
        wapay.value.encryptContent = json.encryptContent
        wapay.value.wapURL = json.wapURL
        wapay.value.bizOrderId = json.storeOrderId
      } else {
        formSubmit(json.wapURL, { param: json.encryptContent })
        if (orderVoInfo.value.isDirectPay) {
          buyProductNow.set('')
          buyProductNowSession.set('')
        }
      }
    } else {
      if (err.code === '3008') {
        // 处理赠品已赠完的情况
        const removeGifts = () => {
          // 找到第一个匹配err.data的sku
          orderCartList.value.forEach(supplier =>
            supplier.goodsList.forEach(product => {
              if (product.rawData?.cartSkuId === err.data) {
                if (product.rawData?.goods?.skuList) {
                  product.rawData.goods.skuList.forEach(item => {
                    item.giftList = []
                  })
                }
              }
            })
          )

          // 更改传递参数
          orderVoInfo.value.cartGoodsList.forEach(item => {
            if (item.cartSkuId === err.data) {
              item.skuNeedGift = false
            }
          })
          isRemoveGifts.value = true
        }

        $alert({
          message: `您购买的商品: ${err.msg}。赠品已赠完，是否继续购买？`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          messageAlign: 'left',
          onConfirmCallback: () => {
            removeGifts()
          },
          onCancelCallback: () => {
            // 用户取消
          }
        })
      } else {
        showToast(err.msg)
      }
    }
  } catch (error) {
    closeToast()
    console.error('提交订单失败:', error)
    showToast('提交订单失败，请重试')
  }
}, 1000) // 1秒内只能提交一次

// 页面初始化
onMounted(async () => {
  await initOrderData()
})
</script>

<style scoped lang="less">
.order-confirm-page {
  min-height: 100vh;
  background-color: @bg-color-gray;
  padding: 8px 10px;
  box-sizing: border-box;

  // 地址选择区域
  .delivery-address-section {
    background-color: @bg-color-white;
    margin-bottom: 8px;
    border-radius: @radius-10;

    .delivery-address-card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 13px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:active {
        background-color: rgba(0, 0, 0, 0.02);
      }

      .delivery-address-content {
        flex: 1;

        .delivery-address-region {
          font-size: @font-size-13;
          color: @text-color-tertiary;
          line-height: 1.5;
          margin-bottom: 2px;
        }

        .delivery-address-detail {
          font-size: @font-size-16;
          color: @text-color-primary;
          font-weight: @font-weight-600;
          line-height: 1.5;
          margin-bottom: 8px;
        }

        .delivery-address-contact {
          display: flex;
          align-items: center;
          font-size: @font-size-13;
          color: @text-color-tertiary;
          line-height: 1.5;

          .contact-name {
            margin-right: 12px;
          }

          .contact-phone {
            // 电话号码样式
          }
        }
      }

      .delivery-address-placeholder {
        flex: 1;
        display: flex;
        align-items: center;

        .placeholder-icon {
          margin-right: 10px;
          flex-shrink: 0;

          .location-icon {
            width: 14px;
            height: 15px;
            display: block;
          }
        }

        .placeholder-content {
          flex: 1;

          .placeholder-title {
            font-size: @font-size-16;
            color: @text-color-secondary;
            font-weight: @font-weight-500;
            margin-bottom: 2px;
            line-height: 1.4;
          }

          .placeholder-subtitle {
            font-size: @font-size-13;
            color: @text-color-tertiary;
            line-height: 1.4;
          }
        }
      }

      .address-arrow-icon {
        margin-left: 10px;
        width: 6px;
        height: 12px;
        flex-shrink: 0;
        opacity: 0.6;
      }
    }
  }

  // 商品列表区域
  .order-goods-section {
    margin-bottom: 8px;
  }

  // 订单汇总区域
  .order-summary-section {
    margin-bottom: 8px;

    :deep(.order-activity-text) {
      color: @color-orange;
      font-weight: @font-weight-500;
    }

    :deep(.order-activity-no-text) {
      color: @text-color-tertiary;
      font-weight: @font-weight-400;
    }
  }

  // 底部操作区域
  .order-submit-section {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;

    .order-submit-info {
      flex: 1;
      min-width: 0;

      .order-submit-price {
        font-size: @font-size-14;
        color: @text-color-primary;
        display: block;
        margin-bottom: 4px;
        line-height: 1.4;
      }

      .order-submit-details {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        span {
          font-size: @font-size-13;
          color: @text-color-tertiary;
          line-height: 1.4;
          white-space: nowrap;
        }
      }
    }
  }
}

// 性能优化：减少重绘和回流
* {
  box-sizing: border-box;
}

// 图片优化
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 文本优化
.delivery-address-detail,
.placeholder-title {
  word-break: break-word;
  overflow-wrap: break-word;
}

// 骨架屏样式
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

// 地址骨架屏
.delivery-address-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 13px;

  .skeleton-content {
    flex: 1;

    .skeleton-region {
      height: 16px;
      width: 120px;
      margin-bottom: 6px;
    }

    .skeleton-detail {
      height: 20px;
      width: 200px;
      margin-bottom: 12px;
    }

    .skeleton-contact {
      display: flex;
      gap: 12px;

      .skeleton-name {
        height: 16px;
        width: 60px;
      }

      .skeleton-phone {
        height: 16px;
        width: 100px;
      }
    }
  }

  .skeleton-arrow {
    width: 6px;
    height: 12px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 2px;
    margin-left: 10px;
  }
}

// 商品列表骨架屏
.goods-list-skeleton {
  background-color: @bg-color-white;
  border-radius: @radius-10;
  padding: 12px;

  .goods-skeleton-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .skeleton-image {
      width: 65px;
      height: 65px;
      background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
      background-size: 200px 100%;
      animation: skeleton-loading 1.5s infinite;
      border-radius: 6px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .skeleton-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .skeleton-name {
        height: 18px;
        width: 80%;
        margin-bottom: 8px;
      }

      .skeleton-spec {
        height: 14px;
        width: 60%;
        margin-bottom: 12px;
      }

      .skeleton-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .skeleton-price {
          height: 16px;
          width: 80px;
        }

        .skeleton-quantity {
          height: 14px;
          width: 40px;
        }
      }
    }
  }
}

// 订单汇总骨架屏
.order-summary-skeleton {
  .summary-skeleton-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .skeleton-label {
      height: 16px;
      width: 80px;
    }

    .skeleton-value {
      height: 16px;
      width: 100px;
    }
  }
}
</style>
